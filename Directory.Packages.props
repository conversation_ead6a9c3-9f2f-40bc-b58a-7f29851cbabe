<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="ClosedXML" Version="0.105.0" />
    <PackageVersion Include="EPPlus" Version="6.0.8" />
    <PackageVersion Include="Microsoft.AspNetCore.JsonPatch" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.9" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.SpaProxy" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.9" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="9.0.4" />
    <PackageVersion Include="Scalar.AspNetCore" Version="2.8.5" />
    <PackageVersion Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />
    <PackageVersion Include="MiniProfiler.EntityFrameworkCore" Version="4.5.4" />
    <PackageVersion Include="MediatR" Version="12.5.0" />
    <PackageVersion Include="MediatR.Contracts" Version="2.0.1" />
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.9" />
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.9" />
    <PackageVersion Include="MailKit" Version="4.13.0" />
    <PackageVersion Include="Mimekit" Version="4.13.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Hybrid" Version="9.9.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OData" Version="9.4.0" />
    <PackageVersion Include="Microsoft.OData.ModelBuilder" Version="2.0.0" />
    <PackageVersion Include="libphonenumber-csharp" Version="9.0.14" />
    <PackageVersion Include="FFMpegCore" Version="5.2.0" />
    <PackageVersion Include="Google.Apis.Auth" Version="1.71.0" />
  </ItemGroup>
</Project>